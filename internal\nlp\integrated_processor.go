package nlp

import (
	"log"
	"strings"
	"sync"
	"time"
)

// IntegratedProcessor 集成的NLP处理器，结合jieba和spago
type IntegratedProcessor struct {
	jiebaProcessor    *JiebaProcessor
	spagoProcessor    *SpagoProcessor
	intentClassifier  *IntentClassifier
	sentimentAnalyzer *SentimentAnalyzer
	entityExtractor   *EntityExtractor
	contextAnalyzer   *ContextAnalyzer
	relationExtractor *RelationExtractor
	initialized       bool
	mutex             sync.RWMutex
}

// IntegratedResult 集成处理结果
type IntegratedResult struct {
	// 基础分词结果
	Tokens    []string `json:"tokens"`
	Keywords  []string `json:"keywords"`
	Entities  []Entity `json:"entities"`
	Topics    []Topic  `json:"topics"`
	Sentiment string   `json:"sentiment"`

	// 高级NLP结果
	Intent            *IntentResult     `json:"intent,omitempty"`
	SentimentDetail   *SentimentResult  `json:"sentiment_detail,omitempty"`
	ExtractedEntities []ExtractedEntity `json:"extracted_entities"`
	Context           *ContextResult    `json:"context,omitempty"`
	Relations         []Relation        `json:"relations"`

	// Spago高级结果
	SpagoResult *SpagoResult `json:"spago_result,omitempty"`

	// 元数据
	ProcessingTime time.Duration `json:"processing_time"`
	Confidence     float64       `json:"confidence"`
	Method         string        `json:"method"` // "jieba", "spago", "integrated", "enhanced"
	QualityScore   float64       `json:"quality_score"`
}

// NewIntegratedProcessor 创建集成处理器
func NewIntegratedProcessor() *IntegratedProcessor {
	log.Printf("🚀 初始化增强集成NLP处理器 (Jieba + Spago + 高级NLP)")

	processor := &IntegratedProcessor{
		initialized: false,
	}

	// 同步初始化jieba（避免CGO并发问题），其他组件异步初始化
	processor.initializeJiebaSync()

	// 异步初始化其他组件
	go processor.initializeAsync()

	return processor
}

// initializeJiebaSync 同步初始化jieba（避免CGO并发问题）
func (ip *IntegratedProcessor) initializeJiebaSync() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("⚠️ Jieba同步初始化失败: %v", r)
		}
	}()

	log.Printf("🔧 同步初始化Jieba处理器...")
	ip.jiebaProcessor = NewJiebaProcessor()
	log.Printf("   ✅ Jieba处理器同步初始化完成")
}

// initializeAsync 异步初始化
func (ip *IntegratedProcessor) initializeAsync() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("⚠️ 集成处理器初始化失败: %v", r)
			ip.initialized = false
		}
	}()

	log.Printf("🔧 开始初始化增强集成处理器组件...")

	// 1. Jieba处理器已在同步初始化中完成
	log.Printf("   ✅ Jieba处理器已同步初始化")

	// 2. 初始化spago处理器
	ip.spagoProcessor = NewSpagoProcessor()
	log.Printf("   ✅ Spago处理器初始化完成")

	// 3. 初始化高级NLP组件
	ip.intentClassifier = NewIntentClassifier()
	log.Printf("   ✅ 意图分类器初始化完成")

	ip.sentimentAnalyzer = NewSentimentAnalyzer()
	log.Printf("   ✅ 情感分析器初始化完成")

	ip.entityExtractor = NewEntityExtractor()
	log.Printf("   ✅ 实体抽取器初始化完成")

	ip.contextAnalyzer = NewContextAnalyzer()
	log.Printf("   ✅ 上下文分析器初始化完成")

	ip.relationExtractor = NewRelationExtractor()
	log.Printf("   ✅ 关系抽取器初始化完成")

	// 等待所有组件完全初始化
	time.Sleep(time.Second * 3)

	ip.mutex.Lock()
	ip.initialized = true
	ip.mutex.Unlock()

	log.Printf("✅ 增强集成NLP处理器初始化完成")
}

// ProcessText 处理文本
func (ip *IntegratedProcessor) ProcessText(text string) *IntegratedResult {
	startTime := time.Now()

	ip.mutex.RLock()
	isInitialized := ip.initialized
	ip.mutex.RUnlock()

	if !isInitialized {
		log.Printf("⚠️ 增强集成处理器未完全初始化，使用基础处理")
		return ip.basicProcess(text)
	}

	// 检查空文本
	if strings.TrimSpace(text) == "" {
		log.Printf("⚠️ 输入文本为空，返回默认结果")
		return &IntegratedResult{
			Tokens:         []string{},
			Keywords:       []string{},
			Entities:       []Entity{},
			Topics:         []Topic{},
			Sentiment:      "中性",
			Confidence:     0.0,
			Method:         "empty_input",
			ProcessingTime: time.Since(startTime),
		}
	}

	// 安全地截取文本用于日志
	logText := text
	if len(text) > 50 {
		logText = text[:50] + "..."
	}
	log.Printf("🧠 增强集成NLP处理: %s", logText)

	result := &IntegratedResult{
		ProcessingTime: time.Since(startTime),
	}

	// 1. 使用jieba进行基础中文处理
	if ip.jiebaProcessor != nil {
		result.Tokens = ip.jiebaProcessor.SegmentText(text)

		keywords := ip.jiebaProcessor.ExtractKeywords(text, 10)
		result.Keywords = ip.convertKeywordsToStrings(keywords)

		result.Entities = ip.jiebaProcessor.ExtractEntities(text)
		result.Topics = ip.jiebaProcessor.ExtractTopics(text, 5)

		sentiment, _ := ip.jiebaProcessor.AnalyzeSentiment(text)
		result.Sentiment = sentiment

		result.Method = "jieba"
		log.Printf("   ✅ Jieba处理完成: %d tokens, %d keywords", len(result.Tokens), len(result.Keywords))
	}

	// 2. 使用spago进行高级处理
	if ip.spagoProcessor != nil {
		spagoResult := ip.spagoProcessor.ProcessText(text)
		result.SpagoResult = spagoResult

		// 如果jieba没有处理，使用spago的结果
		if result.Tokens == nil || len(result.Tokens) == 0 {
			result.Tokens = spagoResult.Tokens
		}

		// 融合置信度
		if result.Method == "jieba" {
			result.Method = "integrated"
			result.Confidence = (0.6 + spagoResult.Confidence*0.4) // jieba权重0.6，spago权重0.4
		} else {
			result.Method = "spago"
			result.Confidence = spagoResult.Confidence
		}

		log.Printf("   ✅ Spago处理完成: 置信度=%.3f", spagoResult.Confidence)
	}

	// 3. 执行高级NLP分析
	ip.performAdvancedNLPAnalysis(text, result)

	// 4. 如果都没有处理成功，使用基础处理
	if result.Tokens == nil || len(result.Tokens) == 0 {
		return ip.basicProcess(text)
	}

	result.ProcessingTime = time.Since(startTime)
	log.Printf("✅ 增强集成处理完成: 方法=%s, 置信度=%.3f, 质量=%.3f, 耗时=%v",
		result.Method, result.Confidence, result.QualityScore, result.ProcessingTime)

	return result
}

// basicProcess 基础处理
func (ip *IntegratedProcessor) basicProcess(text string) *IntegratedResult {
	startTime := time.Now()

	result := &IntegratedResult{
		Tokens:            strings.Fields(text),
		Keywords:          []string{},
		Entities:          []Entity{},
		Topics:            []Topic{},
		Sentiment:         "中性",
		ExtractedEntities: []ExtractedEntity{},
		Relations:         []Relation{},
		SpagoResult:       nil,
		ProcessingTime:    time.Millisecond * 5,
		Confidence:        0.3,
		Method:            "basic",
		QualityScore:      0.2,
	}

	// 即使在基础处理中，也尝试执行高级NLP分析
	if ip.initialized {
		ip.performAdvancedNLPAnalysis(text, result)
		result.Method = "basic_enhanced"
		result.ProcessingTime = time.Since(startTime)
	}

	return result
}

// convertKeywordsToStrings 转换关键词为字符串数组
func (ip *IntegratedProcessor) convertKeywordsToStrings(keywords []WordInfo) []string {
	result := make([]string, len(keywords))
	for i, kw := range keywords {
		result[i] = kw.Word
	}
	return result
}

// Close 关闭处理器
func (ip *IntegratedProcessor) Close() {
	if ip.jiebaProcessor != nil {
		ip.jiebaProcessor.Close()
	}
	if ip.spagoProcessor != nil {
		ip.spagoProcessor.Close()
	}
}

// IsInitialized 检查是否已初始化
func (ip *IntegratedProcessor) IsInitialized() bool {
	ip.mutex.RLock()
	defer ip.mutex.RUnlock()
	return ip.initialized
}

// GetProcessorStatus 获取处理器状态
func (ip *IntegratedProcessor) GetProcessorStatus() map[string]interface{} {
	status := map[string]interface{}{
		"initialized":     ip.IsInitialized(),
		"jieba_available": ip.jiebaProcessor != nil && ip.jiebaProcessor.jieba != nil,
		"spago_available": ip.spagoProcessor != nil,
	}

	if ip.spagoProcessor != nil {
		ip.spagoProcessor.mutex.RLock()
		status["spago_initialized"] = ip.spagoProcessor.initialized
		ip.spagoProcessor.mutex.RUnlock()
	}

	return status
}

// performAdvancedNLPAnalysis 执行高级NLP分析
func (ip *IntegratedProcessor) performAdvancedNLPAnalysis(text string, result *IntegratedResult) {
	log.Printf("   🧠 开始高级NLP分析...")

	// 1. 意图识别
	if ip.intentClassifier != nil {
		intentResult := ip.intentClassifier.ClassifyIntent(text)
		result.Intent = &intentResult
		log.Printf("     意图: %s (%.2f)", result.Intent.Intent, result.Intent.Confidence)
	}

	// 2. 详细情感分析
	if ip.sentimentAnalyzer != nil {
		sentimentResult := ip.sentimentAnalyzer.AnalyzeSentiment(text)
		result.SentimentDetail = &sentimentResult
		result.Sentiment = result.SentimentDetail.Label // 保持向后兼容
		log.Printf("     情感: %s (%.2f)", result.SentimentDetail.Label, result.SentimentDetail.Score)
	}

	// 3. 实体抽取
	if ip.entityExtractor != nil {
		result.ExtractedEntities = ip.entityExtractor.ExtractEntities(text)
		log.Printf("     实体: %d 个", len(result.ExtractedEntities))
	}

	// 4. 上下文分析
	if ip.contextAnalyzer != nil {
		var intentForContext IntentResult
		if result.Intent != nil {
			intentForContext = *result.Intent
		}
		contextResult := ip.contextAnalyzer.AnalyzeContext(text, result.Entities, intentForContext)
		result.Context = &contextResult
		log.Printf("     上下文: %s/%s", result.Context.Domain, result.Context.Topic)
	}

	// 5. 关系抽取
	if ip.relationExtractor != nil {
		result.Relations = ip.relationExtractor.ExtractRelations(text, result.Entities)
		log.Printf("     关系: %d 个", len(result.Relations))
	}

	// 6. 计算质量分数
	result.QualityScore = ip.calculateQualityScore(result)

	// 6. 更新处理方法
	if result.Method == "integrated" {
		result.Method = "enhanced"
	} else if result.Method == "jieba" {
		result.Method = "enhanced_jieba"
	} else if result.Method == "spago" {
		result.Method = "enhanced_spago"
	}

	log.Printf("   ✅ 高级NLP分析完成: 质量分数=%.2f", result.QualityScore)
}

// calculateQualityScore 计算质量分数
func (ip *IntegratedProcessor) calculateQualityScore(result *IntegratedResult) float64 {
	score := 0.0

	// 基础分词质量
	if len(result.Tokens) > 0 {
		score += 0.2
	}

	// 关键词提取质量
	if len(result.Keywords) > 0 {
		score += 0.15
	}

	// 实体识别质量
	if len(result.ExtractedEntities) > 0 {
		avgEntityConfidence := 0.0
		for _, entity := range result.ExtractedEntities {
			avgEntityConfidence += entity.Confidence
		}
		avgEntityConfidence /= float64(len(result.ExtractedEntities))
		score += 0.2 * avgEntityConfidence
	}

	// 意图识别质量
	if result.Intent != nil && result.Intent.Confidence > 0 {
		score += 0.15 * result.Intent.Confidence
	}

	// 情感分析质量
	if result.SentimentDetail != nil && result.SentimentDetail.Confidence > 0 {
		score += 0.1 * result.SentimentDetail.Confidence
	}

	// 上下文理解质量
	contextScore := 0.0
	if result.Context != nil {
		if result.Context.Domain != "unknown" {
			contextScore += 0.5
		}
		if result.Context.Topic != "unknown" {
			contextScore += 0.5
		}
	}
	score += 0.08 * contextScore

	// 关系抽取质量
	if len(result.Relations) > 0 {
		avgRelationConfidence := 0.0
		for _, relation := range result.Relations {
			avgRelationConfidence += relation.Confidence
		}
		avgRelationConfidence /= float64(len(result.Relations))
		score += 0.07 * avgRelationConfidence
	}

	// Spago处理质量
	if result.SpagoResult != nil && result.SpagoResult.Confidence > 0 {
		score += 0.1 * result.SpagoResult.Confidence
	}

	return score
}
