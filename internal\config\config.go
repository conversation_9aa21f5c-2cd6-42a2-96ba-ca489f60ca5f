package config

import (
	"fmt"
	"os"

	"gopkg.in/yaml.v3"
)

// Config 系统配置 - 基于原来的配置结构
type Config struct {
	LocalAI struct {
		BaseURL     string `yaml:"base_url"`
		EmbedModel  string `yaml:"embed_model"`
		ChatModel   string `yaml:"chat_model"`
		Timeout     int    `yaml:"timeout"`
		AutoInstall bool   `yaml:"auto_install"`
	} `yaml:"localai"`

	MySQL struct {
		DSN        string `yaml:"dsn"`
		Host       string `yaml:"host"`
		Port       string `yaml:"port"`
		Username   string `yaml:"username"`
		Password   string `yaml:"password"`
		Database   string `yaml:"database"`
		MaxRetries int    `yaml:"max_retries"`
	} `yaml:"mysql"`

	VectorStore struct {
		Path string `yaml:"path"`
		TopK int    `yaml:"top_k"`
	} `yaml:"vectorstore"`

	Server struct {
		Port string `yaml:"port"`
		Host string `yaml:"host"`
	} `yaml:"server"`
}

// Load 加载配置 - 基于原来的LoadConfig函数
func Load() *Config {
	// 设置默认值 - 使用原来工作的配置
	config := &Config{}
	config.LocalAI.BaseURL = "http://localhost:8080"
	config.LocalAI.EmbedModel = "bge-small-en"
	config.LocalAI.ChatModel = "mistral-7b-instruct-v0.1"
	config.LocalAI.Timeout = 30
	config.LocalAI.AutoInstall = true
	config.MySQL.DSN = "root:park%25123456@tcp(127.0.0.1:33508)/faqdb?charset=utf8mb4&parseTime=True&loc=Local"
	config.MySQL.Host = "127.0.0.1"
	config.MySQL.Port = "33508"
	config.MySQL.Username = "root"
	config.MySQL.Password = "park%123456"
	config.MySQL.Database = "faqdb"
	config.MySQL.MaxRetries = 3
	config.VectorStore.Path = "vectors.db"
	config.VectorStore.TopK = 3
	config.Server.Port = "8082" // 使用8082端口，避免冲突
	config.Server.Host = "0.0.0.0"

	// 环境变量覆盖
	if port := os.Getenv("PORT"); port != "" {
		config.Server.Port = port
	}
	if dsn := os.Getenv("MYSQL_DSN"); dsn != "" {
		config.MySQL.DSN = dsn
	}
	if baseURL := os.Getenv("LOCALAI_BASE_URL"); baseURL != "" {
		config.LocalAI.BaseURL = baseURL
	}

	return config
}

// LoadConfig 从文件加载配置
func LoadConfig(configPath string) (*Config, error) {
	config := Load() // 先加载默认配置

	// 如果配置文件存在，则加载配置
	if _, err := os.Stat(configPath); err == nil {
		data, err := os.ReadFile(configPath)
		if err != nil {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}

		if err := yaml.Unmarshal(data, config); err != nil {
			return nil, fmt.Errorf("failed to parse config file: %w", err)
		}
	}

	return config, nil
}

// GetDSN 获取MySQL连接字符串 - 直接使用配置中的DSN
func (c *Config) GetDSN() string {
	return c.MySQL.DSN
}

// GetServerDSN 获取MySQL服务器连接字符串（不指定数据库）
func (c *Config) GetServerDSN() string {
	return "root:park%25123456@tcp(127.0.0.1:33508)/?charset=utf8mb4&parseTime=True&loc=Local"
}

// GetServerAddress 获取服务器监听地址
func (c *Config) GetServerAddress() string {
	return c.Server.Host + ":" + c.Server.Port
}
