package learning

import (
	"database/sql"
	"encoding/json"
	"math"
	"strings"
	"time"

	"faq-system/internal/logger"
)

// SearchLearnedKnowledge 搜索学习到的知识 - 使用NLP增强搜索
func (kl *KnowledgeLearner) SearchLearnedKnowledge(query string, limit int) ([]*LearnedKnowledge, error) {
	logger.Infof("🔍 开始NLP增强搜索学习知识: %s", query)

	// 1. 首先尝试knowledge_vectors表的向量搜索（最精确）
	if kl.hasVectorData() {
		logger.Infof("🔍 尝试knowledge_vectors向量搜索...")
		vectorResults, err := kl.vectorSearchFromKnowledgeVectors(query, limit)
		if err != nil {
			logger.Warnf("knowledge_vectors向量搜索失败: %v", err)
		} else if len(vectorResults) > 0 {
			// 使用NLP分析提高匹配质量
			enhancedResults := kl.enhanceResultsWithNLP(query, vectorResults)
			if len(enhancedResults) > 0 {
				logger.Infof("✅ knowledge_vectors向量搜索+NLP增强找到 %d 个结果", len(enhancedResults))
				return enhancedResults, nil
			}
		} else {
			logger.Infof("knowledge_vectors向量搜索未找到结果")
		}
	} else {
		logger.Infof("knowledge_vectors表无数据，使用NLP文本搜索")
	}

	// 2. 使用NLP增强的文本搜索
	logger.Infof("🔍 尝试NLP增强文本搜索...")
	results, err := kl.nlpEnhancedTextSearch(query, limit)
	if err != nil {
		logger.Errorf("NLP增强文本搜索失败: %v", err)
		// 3. 最后回退到基础文本搜索
		logger.Infof("🔍 回退到基础文本搜索...")
		basicResults, basicErr := kl.textSearchKnowledge(query, limit)
		if basicErr != nil {
			logger.Errorf("基础文本搜索也失败: %v", basicErr)
			return nil, basicErr
		}
		logger.Infof("✅ 基础文本搜索找到 %d 个结果", len(basicResults))
		return basicResults, nil
	}
	logger.Infof("✅ NLP增强文本搜索找到 %d 个结果", len(results))
	return results, nil
}

// vectorSearchKnowledge 向量搜索学习知识
func (kl *KnowledgeLearner) vectorSearchKnowledge(query string, limit int) ([]*LearnedKnowledge, error) {
	// 生成查询向量
	queryVector, err := kl.embedClient.EmbedText(query)
	if err != nil {
		return nil, err
	}

	// 获取所有知识向量并计算相似度
	vectorQuery := `
		SELECT lk.id, lk.question, lk.answer, lk.source, lk.confidence, 
		       lk.category, lk.keywords, lk.context, lk.learned_from, 
		       lk.status, lk.created_at, kv.vector_data
		FROM learned_knowledge lk
		JOIN knowledge_vectors kv ON lk.id = kv.knowledge_id
		WHERE lk.status IN ('approved', 'pending')
		ORDER BY lk.confidence DESC
	`

	rows, err := kl.db.Query(vectorQuery)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	type knowledgeWithVector struct {
		knowledge  *LearnedKnowledge
		vector     []float32
		similarity float32
	}

	var candidates []knowledgeWithVector

	for rows.Next() {
		var knowledge LearnedKnowledge
		var vectorJSON, keywordsJSON string
		var createdAt time.Time

		err := rows.Scan(&knowledge.ID, &knowledge.Question, &knowledge.Answer,
			&knowledge.Source, &knowledge.Confidence, &knowledge.Category,
			&keywordsJSON, &knowledge.Context, &knowledge.LearnedFrom,
			&knowledge.Status, &createdAt, &vectorJSON)
		if err != nil {
			continue
		}

		knowledge.CreatedAt = createdAt
		json.Unmarshal([]byte(keywordsJSON), &knowledge.Keywords)

		// 解析向量
		var vector []float32
		if err := json.Unmarshal([]byte(vectorJSON), &vector); err != nil {
			continue
		}

		// 计算相似度
		similarity := cosineSimilarity(queryVector, vector)

		candidates = append(candidates, knowledgeWithVector{
			knowledge:  &knowledge,
			vector:     vector,
			similarity: similarity,
		})
	}

	// 按相似度排序
	for i := 0; i < len(candidates)-1; i++ {
		for j := i + 1; j < len(candidates); j++ {
			if candidates[i].similarity < candidates[j].similarity {
				candidates[i], candidates[j] = candidates[j], candidates[i]
			}
		}
	}

	// 返回前N个结果
	if limit > len(candidates) {
		limit = len(candidates)
	}

	results := make([]*LearnedKnowledge, limit)
	for i := 0; i < limit; i++ {
		results[i] = candidates[i].knowledge
	}

	return results, nil
}

// textSearchKnowledge 文本搜索学习知识
func (kl *KnowledgeLearner) textSearchKnowledge(query string, limit int) ([]*LearnedKnowledge, error) {
	// 先检查数据库中是否有学习知识
	var totalCount int
	countQuery := "SELECT COUNT(*) FROM learned_knowledge WHERE status IN ('approved', 'pending')"
	err := kl.db.QueryRow(countQuery).Scan(&totalCount)
	if err != nil {
		logger.Errorf("检查学习知识总数失败: %v", err)
	} else {
		logger.Infof("📊 数据库中共有 %d 条可用学习知识", totalCount)
	}

	// 使用应用层搜索避免数据库字符集问题
	logger.Infof("🔍 使用应用层搜索避免字符集问题")
	return kl.fallbackTextSearch(query, limit)

}

// cosineSimilarity 计算余弦相似度
func cosineSimilarity(a, b []float32) float32 {
	if len(a) != len(b) {
		return 0
	}

	var dotProduct, normA, normB float32
	for i := 0; i < len(a); i++ {
		dotProduct += a[i] * b[i]
		normA += a[i] * a[i]
		normB += b[i] * b[i]
	}

	if normA == 0 || normB == 0 {
		return 0
	}

	return dotProduct / (float32(math.Sqrt(float64(normA))) * float32(math.Sqrt(float64(normB))))
}

// ApproveKnowledge 批准学习到的知识
func (kl *KnowledgeLearner) ApproveKnowledge(knowledgeID int) error {
	query := `UPDATE learned_knowledge SET status = 'approved' WHERE id = ?`
	_, err := kl.db.Exec(query, knowledgeID)
	if err != nil {
		return err
	}

	logger.Infof("✅ 知识 %d 已批准", knowledgeID)
	return nil
}

// GetPendingKnowledge 获取待审核的知识
func (kl *KnowledgeLearner) GetPendingKnowledge(limit int) ([]*LearnedKnowledge, error) {
	query := `
		SELECT id, question, answer, source, confidence, category, 
		       keywords, context, learned_from, status, created_at
		FROM learned_knowledge
		WHERE status = 'pending'
		ORDER BY confidence DESC, created_at DESC
		LIMIT ?
	`

	rows, err := kl.db.Query(query, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var results []*LearnedKnowledge
	for rows.Next() {
		var knowledge LearnedKnowledge
		var keywordsJSON string
		var createdAt time.Time

		err := rows.Scan(&knowledge.ID, &knowledge.Question, &knowledge.Answer,
			&knowledge.Source, &knowledge.Confidence, &knowledge.Category,
			&keywordsJSON, &knowledge.Context, &knowledge.LearnedFrom,
			&knowledge.Status, &createdAt)
		if err != nil {
			continue
		}

		knowledge.CreatedAt = createdAt
		json.Unmarshal([]byte(keywordsJSON), &knowledge.Keywords)
		results = append(results, &knowledge)
	}

	return results, nil
}

// RecordKnowledgeUsage 记录知识使用情况
func (kl *KnowledgeLearner) RecordKnowledgeUsage(knowledgeID int, queryID int64, userID string, matchScore float32, wasHelpful *bool) error {
	query := `
		INSERT INTO knowledge_usage 
		(knowledge_id, query_id, user_id, match_score, was_helpful)
		VALUES (?, ?, ?, ?, ?)
	`

	_, err := kl.db.Exec(query, knowledgeID, queryID, userID, matchScore, wasHelpful)
	if err != nil {
		return err
	}

	// 更新知识的使用统计
	updateQuery := `
		UPDATE learned_knowledge 
		SET usage_count = usage_count + 1,
		    last_used = NOW()
		WHERE id = ?
	`
	kl.db.Exec(updateQuery, knowledgeID)

	return nil
}

// GetKnowledgeStats 获取知识统计信息
func (kl *KnowledgeLearner) GetKnowledgeStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总知识数量
	var totalCount int
	kl.db.QueryRow("SELECT COUNT(*) FROM learned_knowledge").Scan(&totalCount)
	stats["total_knowledge"] = totalCount

	// 按状态分组
	statusQuery := `
		SELECT status, COUNT(*) as count 
		FROM learned_knowledge 
		GROUP BY status
	`
	rows, err := kl.db.Query(statusQuery)
	if err == nil {
		statusStats := make(map[string]int)
		for rows.Next() {
			var status string
			var count int
			rows.Scan(&status, &count)
			statusStats[status] = count
		}
		rows.Close()
		stats["by_status"] = statusStats
	}

	// 按分类分组
	categoryQuery := `
		SELECT category, COUNT(*) as count 
		FROM learned_knowledge 
		GROUP BY category
	`
	rows, err = kl.db.Query(categoryQuery)
	if err == nil {
		categoryStats := make(map[string]int)
		for rows.Next() {
			var category string
			var count int
			rows.Scan(&category, &count)
			categoryStats[category] = count
		}
		rows.Close()
		stats["by_category"] = categoryStats
	}

	// 最近学习的知识
	var recentCount int
	kl.db.QueryRow("SELECT COUNT(*) FROM learned_knowledge WHERE created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)").Scan(&recentCount)
	stats["recent_learned"] = recentCount

	return stats, nil
}

// fallbackTextSearch 备用文本搜索方法，避免字符集问题
func (kl *KnowledgeLearner) fallbackTextSearch(query string, limit int) ([]*LearnedKnowledge, error) {
	logger.Infof("🔄 使用备用搜索方案")

	// 最简单的查询，不使用任何字符串匹配函数
	searchQuery := `
		SELECT id, question, answer, source, confidence, category,
		       keywords, context, learned_from, status, created_at
		FROM learned_knowledge
		WHERE status IN ('approved', 'pending')
		ORDER BY confidence DESC, created_at DESC
		LIMIT ?
	`

	rows, err := kl.db.Query(searchQuery, limit*3) // 获取更多结果用于过滤
	if err != nil {
		logger.Errorf("备用搜索也失败: %v", err)
		return nil, err
	}
	defer rows.Close()

	var allResults []*LearnedKnowledge
	for rows.Next() {
		var knowledge LearnedKnowledge
		var keywordsJSON string
		var createdAt time.Time
		var learnedFrom sql.NullString // 使用sql.NullString处理NULL值

		err := rows.Scan(&knowledge.ID, &knowledge.Question, &knowledge.Answer,
			&knowledge.Source, &knowledge.Confidence, &knowledge.Category,
			&keywordsJSON, &knowledge.Context, &learnedFrom,
			&knowledge.Status, &createdAt)
		if err != nil {
			logger.Errorf("扫描行失败: %v", err)
			continue
		}

		// 处理NULL值
		if learnedFrom.Valid {
			knowledge.LearnedFrom = learnedFrom.String
		} else {
			knowledge.LearnedFrom = ""
		}

		knowledge.CreatedAt = createdAt
		json.Unmarshal([]byte(keywordsJSON), &knowledge.Keywords)
		allResults = append(allResults, &knowledge)
	}

	// 在应用层进行文本匹配过滤
	var results []*LearnedKnowledge
	queryLower := strings.ToLower(query)

	for _, knowledge := range allResults {
		questionLower := strings.ToLower(knowledge.Question)
		answerLower := strings.ToLower(knowledge.Answer)

		if strings.Contains(questionLower, queryLower) || strings.Contains(answerLower, queryLower) {
			results = append(results, knowledge)
			if len(results) >= limit {
				break
			}
		}
	}

	logger.Infof("✅ 备用搜索找到 %d 个匹配结果", len(results))
	return results, nil
}

// enhanceResultsWithNLP 使用NLP分析增强搜索结果
func (kl *KnowledgeLearner) enhanceResultsWithNLP(query string, results []*LearnedKnowledge) []*LearnedKnowledge {
	if len(results) == 0 {
		return results
	}

	logger.Infof("🧠 使用NLP分析增强 %d 个搜索结果", len(results))

	// 对每个结果进行NLP分析评分
	type scoredResult struct {
		knowledge *LearnedKnowledge
		nlpScore  float64
	}

	var scoredResults []scoredResult
	queryLower := strings.ToLower(query)

	for _, knowledge := range results {
		score := kl.calculateNLPScore(queryLower, knowledge)
		if score > 0.1 { // 只保留有一定相关性的结果
			scoredResults = append(scoredResults, scoredResult{
				knowledge: knowledge,
				nlpScore:  score,
			})
		}
	}

	// 按NLP评分排序
	for i := 0; i < len(scoredResults)-1; i++ {
		for j := i + 1; j < len(scoredResults); j++ {
			if scoredResults[i].nlpScore < scoredResults[j].nlpScore {
				scoredResults[i], scoredResults[j] = scoredResults[j], scoredResults[i]
			}
		}
	}

	// 返回增强后的结果
	var enhancedResults []*LearnedKnowledge
	for _, scored := range scoredResults {
		// 更新置信度为NLP评分（修复类型转换）
		scored.knowledge.Confidence = float32(scored.nlpScore)
		enhancedResults = append(enhancedResults, scored.knowledge)
	}

	logger.Infof("✅ NLP增强完成，保留 %d 个高质量结果", len(enhancedResults))
	return enhancedResults
}

// calculateNLPScore 计算NLP相关性评分 - 针对中文优化
func (kl *KnowledgeLearner) calculateNLPScore(query string, knowledge *LearnedKnowledge) float64 {
	score := 0.0
	queryLower := strings.ToLower(query)
	questionLower := strings.ToLower(knowledge.Question)
	answerLower := strings.ToLower(knowledge.Answer)

	// 1. 精确匹配得分最高（针对中文名字优化）
	if strings.Contains(questionLower, queryLower) {
		score += 0.9 // 问题包含查询词，得分最高
		questionPreview := questionLower
		if len(questionPreview) > 50 {
			questionPreview = questionPreview[:50] + "..."
		}
		logger.Infof("🎯 精确匹配-问题包含查询: %s 在 %s", queryLower, questionPreview)
	}
	if strings.Contains(answerLower, queryLower) {
		score += 0.8 // 答案包含查询词，得分很高
		logger.Infof("🎯 精确匹配-答案包含查询: %s 在答案中", queryLower)
	}
	if strings.Contains(queryLower, questionLower) {
		score += 0.7 // 查询词包含问题，也很相关
		questionPreview2 := questionLower
		if len(questionPreview2) > 50 {
			questionPreview2 = questionPreview2[:50] + "..."
		}
		logger.Infof("🎯 精确匹配-查询包含问题: %s 包含 %s", queryLower, questionPreview2)
	}

	// 2. 中文字符匹配（对于"蔡依林"这样的名字特别重要）
	queryRunes := []rune(queryLower)
	questionRunes := []rune(questionLower)
	answerRunes := []rune(answerLower)

	// 计算中文字符重叠度
	runeOverlapQuestion := kl.calculateRuneOverlap(queryRunes, questionRunes)
	runeOverlapAnswer := kl.calculateRuneOverlap(queryRunes, answerRunes)

	score += runeOverlapQuestion * 0.6
	score += runeOverlapAnswer * 0.5

	// 3. 词汇匹配（分词后的匹配）
	queryWords := strings.Fields(queryLower)
	questionWords := strings.Fields(questionLower)
	answerWords := strings.Fields(answerLower)

	// 计算词汇重叠度
	questionOverlap := kl.calculateWordOverlap(queryWords, questionWords)
	answerOverlap := kl.calculateWordOverlap(queryWords, answerWords)

	score += questionOverlap * 0.4
	score += answerOverlap * 0.3

	// 4. 关键词标签匹配
	if len(knowledge.Keywords) > 0 {
		for _, keyword := range knowledge.Keywords {
			keywordLower := strings.ToLower(keyword)
			if strings.Contains(queryLower, keywordLower) || strings.Contains(keywordLower, queryLower) {
				score += 0.3 // 提高关键词匹配权重
			}
		}
	}

	// 5. 语义相似度（简单实现）
	semanticScore := kl.calculateSemanticSimilarity(queryLower, questionLower)
	score += semanticScore * 0.2

	// 6. 特殊加分：如果是人名查询，给予额外加分
	if kl.isPersonName(queryLower) {
		if strings.Contains(questionLower, queryLower) || strings.Contains(answerLower, queryLower) {
			score += 0.2 // 人名匹配额外加分
		}
	}

	// 限制评分范围
	if score > 1.0 {
		score = 1.0
	}

	return score
}

// calculateWordOverlap 计算词汇重叠度
func (kl *KnowledgeLearner) calculateWordOverlap(words1, words2 []string) float64 {
	if len(words1) == 0 || len(words2) == 0 {
		return 0.0
	}

	// 创建词汇集合
	wordSet1 := make(map[string]bool)
	for _, word := range words1 {
		if len(word) > 1 { // 忽略单字符词
			wordSet1[word] = true
		}
	}

	// 计算重叠词汇数量
	overlap := 0
	for _, word := range words2 {
		if len(word) > 1 && wordSet1[word] {
			overlap++
		}
	}

	// 返回重叠比例
	maxLen := len(words1)
	if len(words2) > maxLen {
		maxLen = len(words2)
	}

	return float64(overlap) / float64(maxLen)
}

// calculateSemanticSimilarity 计算语义相似度（简单实现）
func (kl *KnowledgeLearner) calculateSemanticSimilarity(text1, text2 string) float64 {
	// 简单的语义相似度计算
	// 基于共同子串和编辑距离

	// 1. 最长公共子序列
	lcs := kl.longestCommonSubsequence(text1, text2)
	lcsScore := float64(lcs) / float64(max(len(text1), len(text2)))

	// 2. 编辑距离相似度
	editDist := kl.editDistance(text1, text2)
	maxLen := max(len(text1), len(text2))
	editScore := 1.0 - float64(editDist)/float64(maxLen)

	// 综合评分
	return (lcsScore*0.6 + editScore*0.4)
}

// longestCommonSubsequence 计算最长公共子序列长度
func (kl *KnowledgeLearner) longestCommonSubsequence(text1, text2 string) int {
	m, n := len(text1), len(text2)
	if m == 0 || n == 0 {
		return 0
	}

	// 动态规划表
	dp := make([][]int, m+1)
	for i := range dp {
		dp[i] = make([]int, n+1)
	}

	// 填充DP表
	for i := 1; i <= m; i++ {
		for j := 1; j <= n; j++ {
			if text1[i-1] == text2[j-1] {
				dp[i][j] = dp[i-1][j-1] + 1
			} else {
				dp[i][j] = max(dp[i-1][j], dp[i][j-1])
			}
		}
	}

	return dp[m][n]
}

// editDistance 计算编辑距离
func (kl *KnowledgeLearner) editDistance(text1, text2 string) int {
	m, n := len(text1), len(text2)

	// 创建DP表
	dp := make([][]int, m+1)
	for i := range dp {
		dp[i] = make([]int, n+1)
	}

	// 初始化边界条件
	for i := 0; i <= m; i++ {
		dp[i][0] = i
	}
	for j := 0; j <= n; j++ {
		dp[0][j] = j
	}

	// 填充DP表
	for i := 1; i <= m; i++ {
		for j := 1; j <= n; j++ {
			if text1[i-1] == text2[j-1] {
				dp[i][j] = dp[i-1][j-1]
			} else {
				dp[i][j] = min(dp[i-1][j], dp[i][j-1], dp[i-1][j-1]) + 1
			}
		}
	}

	return dp[m][n]
}

// max 返回两个整数的最大值
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// min 返回三个整数的最小值
func min(a, b, c int) int {
	if a < b && a < c {
		return a
	}
	if b < c {
		return b
	}
	return c
}

// nlpEnhancedTextSearch NLP增强的文本搜索
func (kl *KnowledgeLearner) nlpEnhancedTextSearch(query string, limit int) ([]*LearnedKnowledge, error) {
	logger.Infof("🧠 开始NLP增强文本搜索: %s", query)

	// 获取所有可用的学习知识 - 优先查询包含关键词的数据
	queryPattern := "%" + query + "%"
	searchQuery := `
		SELECT id, question, answer, source, confidence, category,
		       keywords, context, learned_from, status, created_at
		FROM learned_knowledge
		WHERE status IN ('approved', 'pending')
		ORDER BY
			CASE
				WHEN question LIKE ? OR answer LIKE ? OR keywords LIKE ? THEN 0
				ELSE 1
			END,
			confidence DESC,
			created_at DESC
		LIMIT ?
	`

	rows, err := kl.db.Query(searchQuery, queryPattern, queryPattern, queryPattern, limit*50) // 大幅增加候选结果数量
	if err != nil {
		logger.Errorf("查询学习知识失败: %v", err)
		return nil, err
	}
	defer rows.Close()

	var candidates []*LearnedKnowledge
	for rows.Next() {
		var knowledge LearnedKnowledge
		var keywordsJSON string
		var createdAt time.Time
		var learnedFrom sql.NullString

		err := rows.Scan(&knowledge.ID, &knowledge.Question, &knowledge.Answer,
			&knowledge.Source, &knowledge.Confidence, &knowledge.Category,
			&keywordsJSON, &knowledge.Context, &learnedFrom,
			&knowledge.Status, &createdAt)
		if err != nil {
			logger.Errorf("扫描行失败: %v", err)
			continue
		}

		// 处理NULL值
		if learnedFrom.Valid {
			knowledge.LearnedFrom = learnedFrom.String
		} else {
			knowledge.LearnedFrom = ""
		}

		knowledge.CreatedAt = createdAt
		json.Unmarshal([]byte(keywordsJSON), &knowledge.Keywords)
		candidates = append(candidates, &knowledge)
	}

	logger.Infof("📊 获取到 %d 个候选知识进行NLP分析", len(candidates))

	// 使用NLP分析对所有候选结果进行评分
	type scoredKnowledge struct {
		knowledge *LearnedKnowledge
		nlpScore  float64
	}

	var scoredResults []scoredKnowledge
	queryLower := strings.ToLower(query)

	for _, knowledge := range candidates {
		score := kl.calculateNLPScore(queryLower, knowledge)
		questionPreview := knowledge.Question
		if len(questionPreview) > 50 {
			questionPreview = questionPreview[:50] + "..."
		}
		logger.Infof("🔍 候选知识评分: %s -> %.3f", questionPreview, score)

		// 如果包含查询关键词，额外记录详细信息
		if strings.Contains(strings.ToLower(knowledge.Question), queryLower) ||
			strings.Contains(strings.ToLower(knowledge.Answer), queryLower) {
			logger.Infof("🎯 发现关键词匹配: %s", knowledge.Question)
		}

		if score > 0.05 { // 大幅降低阈值，包含更多可能相关的结果
			scoredResults = append(scoredResults, scoredKnowledge{
				knowledge: knowledge,
				nlpScore:  score,
			})
		}
	}

	// 按NLP评分排序
	for i := 0; i < len(scoredResults)-1; i++ {
		for j := i + 1; j < len(scoredResults); j++ {
			if scoredResults[i].nlpScore < scoredResults[j].nlpScore {
				scoredResults[i], scoredResults[j] = scoredResults[j], scoredResults[i]
			}
		}
	}

	// 返回前N个最佳结果
	resultLimit := limit
	if len(scoredResults) < resultLimit {
		resultLimit = len(scoredResults)
	}

	var results []*LearnedKnowledge
	for i := 0; i < resultLimit; i++ {
		// 更新置信度为NLP评分
		scoredResults[i].knowledge.Confidence = float32(scoredResults[i].nlpScore)
		results = append(results, scoredResults[i].knowledge)

		questionPreview := scoredResults[i].knowledge.Question
		if len(questionPreview) > 50 {
			questionPreview = questionPreview[:50] + "..."
		}
		logger.Infof("🎯 NLP匹配结果 %d: %s (评分: %.3f)",
			i+1, questionPreview, scoredResults[i].nlpScore)
	}

	logger.Infof("✅ NLP增强文本搜索完成，返回 %d 个高质量结果", len(results))
	return results, nil
}

// calculateRuneOverlap 计算中文字符重叠度
func (kl *KnowledgeLearner) calculateRuneOverlap(runes1, runes2 []rune) float64 {
	if len(runes1) == 0 || len(runes2) == 0 {
		return 0.0
	}

	// 创建字符集合
	runeSet1 := make(map[rune]bool)
	for _, r := range runes1 {
		runeSet1[r] = true
	}

	// 计算重叠字符数量
	overlap := 0
	for _, r := range runes2 {
		if runeSet1[r] {
			overlap++
		}
	}

	// 返回重叠比例
	maxLen := len(runes1)
	if len(runes2) > maxLen {
		maxLen = len(runes2)
	}

	return float64(overlap) / float64(maxLen)
}

// isPersonName 判断是否是人名（简单实现）
func (kl *KnowledgeLearner) isPersonName(query string) bool {
	// 简单的人名判断规则
	runes := []rune(query)

	// 长度在2-4个字符之间，通常是中文人名
	if len(runes) >= 2 && len(runes) <= 4 {
		// 检查是否全部是中文字符
		for _, r := range runes {
			if r < 0x4e00 || r > 0x9fff {
				return false // 不是中文字符
			}
		}
		return true
	}

	return false
}

// hasVectorData 检查是否有向量数据
func (kl *KnowledgeLearner) hasVectorData() bool {
	var count int
	err := kl.db.QueryRow("SELECT COUNT(*) FROM knowledge_vectors").Scan(&count)
	if err != nil {
		logger.Warnf("检查向量数据失败: %v", err)
		return false
	}
	return count > 0
}

// vectorSearchFromKnowledgeVectors 从knowledge_vectors表进行向量搜索
func (kl *KnowledgeLearner) vectorSearchFromKnowledgeVectors(query string, limit int) ([]*LearnedKnowledge, error) {
	// 首先尝试使用NLP处理器生成查询向量
	var queryVector []float32
	var err error

	// 检查是否有NLP处理器
	if kl.nlpProcessor != nil {
		// 使用类型断言检查是否有ProcessText方法
		if processor, ok := kl.nlpProcessor.(interface{ ProcessText(string) interface{} }); ok {
			result := processor.ProcessText(query)
			if result != nil {
				// 从NLP结果生成向量（简化实现）
				queryVector = kl.generateVectorFromNLPResult(result, query)
			}
		}
	}

	// 如果NLP处理器不可用，尝试使用embedClient
	if len(queryVector) == 0 && kl.embedClient != nil {
		queryVector, err = kl.embedClient.EmbedText(query)
		if err != nil {
			logger.Warnf("生成查询向量失败: %v", err)
			return nil, err
		}
	}

	// 如果都不可用，返回空结果
	if len(queryVector) == 0 {
		logger.Infof("无法生成查询向量，跳过向量搜索")
		return []*LearnedKnowledge{}, nil
	}

	// 从knowledge_vectors表查询所有向量数据
	vectorQuery := `
		SELECT lk.id, lk.question, lk.answer, lk.source, lk.confidence,
		       lk.category, lk.keywords, lk.context, lk.learned_from,
		       lk.status, lk.created_at, kv.vector_data
		FROM learned_knowledge lk
		JOIN knowledge_vectors kv ON lk.id = kv.knowledge_id
		WHERE lk.status IN ('approved', 'pending')
		ORDER BY lk.confidence DESC
	`

	rows, err := kl.db.Query(vectorQuery)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	type knowledgeWithVector struct {
		knowledge  *LearnedKnowledge
		vector     []float32
		similarity float32
	}

	var candidates []knowledgeWithVector

	for rows.Next() {
		var knowledge LearnedKnowledge
		var vectorJSON, keywordsJSON string
		var createdAt time.Time

		err := rows.Scan(&knowledge.ID, &knowledge.Question, &knowledge.Answer,
			&knowledge.Source, &knowledge.Confidence, &knowledge.Category,
			&keywordsJSON, &knowledge.Context, &knowledge.LearnedFrom,
			&knowledge.Status, &createdAt, &vectorJSON)
		if err != nil {
			continue
		}

		knowledge.CreatedAt = createdAt
		json.Unmarshal([]byte(keywordsJSON), &knowledge.Keywords)

		// 解析向量
		var vector []float32
		if err := json.Unmarshal([]byte(vectorJSON), &vector); err != nil {
			continue
		}

		// 计算相似度
		similarity := kl.cosineSimilarity(queryVector, vector)

		candidates = append(candidates, knowledgeWithVector{
			knowledge:  &knowledge,
			vector:     vector,
			similarity: similarity,
		})
	}

	// 按相似度排序
	for i := 0; i < len(candidates)-1; i++ {
		for j := i + 1; j < len(candidates); j++ {
			if candidates[i].similarity < candidates[j].similarity {
				candidates[i], candidates[j] = candidates[j], candidates[i]
			}
		}
	}

	// 返回前N个结果
	var results []*LearnedKnowledge
	maxResults := limit
	if maxResults > len(candidates) {
		maxResults = len(candidates)
	}

	for i := 0; i < maxResults; i++ {
		if candidates[i].similarity > 0.3 { // 相似度阈值
			results = append(results, candidates[i].knowledge)
			questionPreview := candidates[i].knowledge.Question
			if len(questionPreview) > 50 {
				questionPreview = questionPreview[:50] + "..."
			}
			logger.Infof("🎯 向量匹配: %s (相似度: %.3f)", questionPreview, candidates[i].similarity)
		}
	}

	return results, nil
}

// generateVectorFromNLPResult 从NLP结果生成向量
func (kl *KnowledgeLearner) generateVectorFromNLPResult(nlpResult interface{}, text string) []float32 {
	// 简化实现：基于文本内容生成模拟向量
	hash := 0
	for _, char := range text {
		hash = hash*31 + int(char)
	}

	// 生成384维向量
	vector := make([]float32, 384)
	for i := range vector {
		hash = (hash*1103515245 + 12345) & 0x7fffffff
		vector[i] = float32(hash%1000-500) / 1000.0
	}

	// 归一化向量
	var norm float32
	for _, val := range vector {
		norm += val * val
	}
	if norm > 0 {
		norm = 1.0 / float32(math.Sqrt(float64(norm)))
		for i := range vector {
			vector[i] *= norm
		}
	}

	return vector
}

// cosineSimilarity 计算余弦相似度
func (kl *KnowledgeLearner) cosineSimilarity(a, b []float32) float32 {
	if len(a) != len(b) {
		return 0
	}

	var dotProduct, normA, normB float32
	for i := range a {
		dotProduct += a[i] * b[i]
		normA += a[i] * a[i]
		normB += b[i] * b[i]
	}

	if normA == 0 || normB == 0 {
		return 0
	}

	return dotProduct / (float32(math.Sqrt(float64(normA))) * float32(math.Sqrt(float64(normB))))
}
